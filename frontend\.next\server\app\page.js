/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ChatContext.tsx */ \"(rsc)/./src/contexts/ChatContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2F1cCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q3N0dWR5JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhdXBcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxzdHVkeVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXVwXFxPbmVEcml2ZVxcRGVza3RvcFxcc3R1ZHlcXGZyb250ZW5kXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9e52cac4ae74\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGF1cFxcT25lRHJpdmVcXERlc2t0b3BcXHN0dWR5XFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWU1MmNhYzRhZTc0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(rsc)/./src/contexts/ChatContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Real-time Chat App\",\n    description: \"A modern real-time chat application built with Next.js and FastAPI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        \"data-theme\": \"light\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased min-h-screen bg-base-100`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_3__.ChatProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\study\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\study\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\study\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/contexts/ChatContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),
/* harmony export */   useChat: () => (/* binding */ useChat)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ChatProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ChatProvider() from the server but ChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\study\\frontend\\src\\contexts\\ChatContext.tsx",
"ChatProvider",
);const useChat = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useChat() from the server but useChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\study\\frontend\\src\\contexts\\ChatContext.tsx",
"useChat",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ChatContext.tsx */ \"(ssr)/./src/contexts/ChatContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CChatContext.tsx%22%2C%22ids%22%3A%5B%22ChatProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2F1cCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q3N0dWR5JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxhdXBcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxzdHVkeVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caup%5C%5COneDrive%5C%5CDesktop%5C%5Cstudy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Lock,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const { isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"hero min-h-[80vh] bg-gradient-to-br from-primary to-info\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-content text-center text-primary-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"mb-5 text-5xl font-bold\",\n                                        children: \"Real-time Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-5 text-lg\",\n                                        children: \"Connect with people instantly through our modern, fast, and secure chat application. Built with cutting-edge technology for the best user experience.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/chat\",\n                                        className: \"btn btn-accent btn-lg\",\n                                        children: \"Go to Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/register\",\n                                                className: \"btn btn-outline btn-lg\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/login\",\n                                                className: \"btn btn-outline btn-lg\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-base-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-4\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-base-content/70\",\n                                            children: \"Everything you need for modern communication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-primary mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"Real-time Messaging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Instant message delivery with WebSocket technology for seamless communication.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-secondary mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"User Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Find and connect with users, create private chats and group conversations.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-accent mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"Admin Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Comprehensive admin tools for user and room management with real-time analytics.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-warning mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"Fast & Responsive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Built with Next.js and optimized for speed with modern web technologies.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-info mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"Cross-platform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Works seamlessly across all devices and browsers with responsive design.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card bg-base-200 shadow-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body items-center text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Lock_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        size: 48,\n                                                        className: \"text-success mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"card-title\",\n                                                        children: \"Secure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"End-to-end security with JWT authentication and secure data transmission.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-base-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-4\",\n                                            children: \"Tech Stack\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-base-content/70\",\n                                            children: \"Built with modern, reliable technologies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-base-100 rounded-lg p-6 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg mb-2\",\n                                                        children: \"Frontend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-primary\",\n                                                                children: \"Next.js 15\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-secondary\",\n                                                                children: \"React 19\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-accent\",\n                                                                children: \"TypeScript\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-neutral\",\n                                                                children: \"Tailwind CSS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-info\",\n                                                                children: \"DaisyUI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-base-100 rounded-lg p-6 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg mb-2\",\n                                                        children: \"Backend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-primary\",\n                                                                children: \"FastAPI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-secondary\",\n                                                                children: \"Python\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-accent\",\n                                                                children: \"SQLAlchemy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-neutral\",\n                                                                children: \"SQLite\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-base-100 rounded-lg p-6 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg mb-2\",\n                                                        children: \"Real-time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-primary\",\n                                                                children: \"WebSocket\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-secondary\",\n                                                                children: \"Socket.IO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-accent\",\n                                                                children: \"Real-time Events\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-base-100 rounded-lg p-6 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg mb-2\",\n                                                        children: \"Security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-primary\",\n                                                                children: \"JWT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-secondary\",\n                                                                children: \"CORS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"badge badge-accent\",\n                                                                children: \"Authentication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-primary text-primary-content\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-4\",\n                                    children: \"Ready to Start Chatting?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg mb-8\",\n                                    children: \"Join our community and experience real-time communication like never before.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-outline btn-lg\",\n                                            children: \"Create Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"btn btn-outline btn-lg\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Braces,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Braces,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Braces,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Braces,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/braces.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    const CurrentYear = 2025;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"footer footer-center p-10 bg-base-200 text-base-content rounded\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"grid grid-flow-col gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/about\",\n                        className: \"link link-hover\",\n                        children: \"About\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/contact\",\n                        className: \"link link-hover\",\n                        children: \"Contact\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/privacy\",\n                        className: \"link link-hover\",\n                        children: \"Privacy\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/terms\",\n                        className: \"link link-hover\",\n                        children: \"Terms\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-flow-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://github.com\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"btn btn-ghost btn-circle\",\n                            \"aria-label\": \"GitHub\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 26,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://twitter.com\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"btn btn-ghost btn-circle\",\n                            \"aria-label\": \"Twitter\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 26,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://linkedin.com\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"btn btn-ghost btn-circle\",\n                            \"aria-label\": \"LinkedIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 26,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            \"Made with using \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Braces_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 18,\n                                className: \"text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 27\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"badge badge-primary\",\n                                children: \"Next.js\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"badge badge-success\",\n                                children: \"FastAPI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"badge badge-accent\",\n                                children: \"Socket.IO\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"badge badge-neutral\",\n                                children: \"DaisyUI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Copyright \\xa9 \",\n                        CurrentYear,\n                        \" - Real-time Chat Application\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,MessageCircle,Settings,Shield,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header() {\n    const { user, logout, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"navbar bg-primary text-primary-content shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"btn btn-ghost text-xl font-bold\",\n                    children: \"\\uD83D\\uDCAC ChatApp\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-center hidden lg:flex\",\n                children: isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"menu menu-horizontal px-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/chat\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"CHAT\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/users\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"USERS\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this),\n                        user?.role === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"ADMIN\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-end\",\n                children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"dropdown dropdown-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            tabIndex: 0,\n                            role: \"button\",\n                            className: \"btn btn-ghost btn-circle avatar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 rounded-full\",\n                                children: user?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    width: 40,\n                                    height: 40,\n                                    alt: user.display_name || user.username,\n                                    src: user.avatar_url,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"avatar placeholder\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-neutral text-neutral-content rounded-full w-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: user?.display_name?.charAt(0) || user?.username?.charAt(0) || 'U'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            tabIndex: 0,\n                            className: \"menu menu-sm dropdown-content bg-base-100 text-base-content rounded-box z-[1] mt-3 w-52 p-2 shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"menu-title\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user?.display_name || user?.username\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/profile\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"PROFILE\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divider my-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"flex items-center gap-2 text-error\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"LOGOUT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/login\",\n                            className: \"btn btn-info text-white\",\n                            children: \"LOGIN\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/register\",\n                            className: \"btn btn-info text-white\",\n                            children: \"REGISTER\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"navbar-start lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"dropdown\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            tabIndex: 0,\n                            role: \"button\",\n                            className: \"btn btn-ghost btn-circle\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M4 6h16M4 12h16M4 18h7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            tabIndex: 0,\n                            className: \"menu menu-sm dropdown-content bg-base-100 text-base-content rounded-box z-[1] mt-3 w-52 p-2 shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/chat\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Chat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/users\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Users\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                user?.role === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_MessageCircle_Settings_Shield_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Admin\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst authReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_LOADING':\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case 'SET_USER':\n            return {\n                ...state,\n                user: action.payload,\n                isAuthenticated: true,\n                isLoading: false\n            };\n        case 'SET_TOKEN':\n            return {\n                ...state,\n                token: action.payload\n            };\n        case 'UPDATE_USER':\n            return {\n                ...state,\n                user: state.user ? {\n                    ...state.user,\n                    ...action.payload\n                } : null\n            };\n        case 'LOGOUT':\n            return {\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            };\n        default:\n            return state;\n    }\n};\nconst initialState = {\n    user: null,\n    token: null,\n    isAuthenticated: false,\n    isLoading: true\n};\nfunction AuthProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(authReducer, initialState);\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        const userData = localStorage.getItem('user');\n                        if (token && userData) {\n                            dispatch({\n                                type: 'SET_TOKEN',\n                                payload: token\n                            });\n                            try {\n                                // Verify token is still valid by fetching current user\n                                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.getCurrentUser();\n                                dispatch({\n                                    type: 'SET_USER',\n                                    payload: user\n                                });\n                            } catch (error) {\n                                // Token is invalid, clear storage\n                                localStorage.removeItem('token');\n                                localStorage.removeItem('user');\n                                dispatch({\n                                    type: 'LOGOUT'\n                                });\n                            }\n                        } else {\n                            dispatch({\n                                type: 'SET_LOADING',\n                                payload: false\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        dispatch({\n                            type: 'SET_LOADING',\n                            payload: false\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (credentials)=>{\n        try {\n            dispatch({\n                type: 'SET_LOADING',\n                payload: true\n            });\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.login(credentials);\n            // Store token and user data\n            localStorage.setItem('token', response.token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            dispatch({\n                type: 'SET_TOKEN',\n                payload: response.token\n            });\n            dispatch({\n                type: 'SET_USER',\n                payload: response.user\n            });\n        } catch (error) {\n            dispatch({\n                type: 'SET_LOADING',\n                payload: false\n            });\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            dispatch({\n                type: 'SET_LOADING',\n                payload: true\n            });\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.register(userData);\n            // Store token and user data\n            localStorage.setItem('token', response.token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            dispatch({\n                type: 'SET_TOKEN',\n                payload: response.token\n            });\n            dispatch({\n                type: 'SET_USER',\n                payload: response.user\n            });\n        } catch (error) {\n            dispatch({\n                type: 'SET_LOADING',\n                payload: false\n            });\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        dispatch({\n            type: 'LOGOUT'\n        });\n    };\n    const updateUser = async (userData)=>{\n        if (!state.user) throw new Error('No user logged in');\n        try {\n            const updatedUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.updateUser(state.user.id, userData);\n            // Update localStorage\n            localStorage.setItem('user', JSON.stringify(updatedUser));\n            dispatch({\n                type: 'UPDATE_USER',\n                payload: userData\n            });\n        } catch (error) {\n            throw error;\n        }\n    };\n    const updatePassword = async (passwordData)=>{\n        if (!state.user) throw new Error('No user logged in');\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.updatePassword(state.user.id, passwordData);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const value = {\n        ...state,\n        login,\n        register,\n        logout,\n        updateUser,\n        updatePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ChatContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/ChatContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_websocket__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/websocket */ \"(ssr)/./src/lib/websocket.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst chatReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_MESSAGES':\n            return {\n                ...state,\n                messages: action.payload\n            };\n        case 'ADD_MESSAGE':\n            return {\n                ...state,\n                messages: [\n                    ...state.messages,\n                    action.payload\n                ].sort((a, b)=>new Date(a.created_at).getTime() - new Date(b.created_at).getTime())\n            };\n        case 'UPDATE_MESSAGE':\n            return {\n                ...state,\n                messages: state.messages.map((msg)=>msg.id === action.payload.id ? {\n                        ...msg,\n                        ...action.payload.updates\n                    } : msg)\n            };\n        case 'DELETE_MESSAGE':\n            return {\n                ...state,\n                messages: state.messages.filter((msg)=>msg.id !== action.payload)\n            };\n        case 'SET_ROOMS':\n            return {\n                ...state,\n                rooms: action.payload\n            };\n        case 'ADD_ROOM':\n            return {\n                ...state,\n                rooms: [\n                    ...state.rooms,\n                    action.payload\n                ]\n            };\n        case 'SET_CURRENT_ROOM':\n            return {\n                ...state,\n                currentRoom: action.payload\n            };\n        case 'SET_ONLINE_USERS':\n            return {\n                ...state,\n                onlineUsers: action.payload\n            };\n        case 'ADD_ONLINE_USER':\n            return {\n                ...state,\n                onlineUsers: state.onlineUsers.find((u)=>u.id === action.payload.id) ? state.onlineUsers : [\n                    ...state.onlineUsers,\n                    action.payload\n                ]\n            };\n        case 'REMOVE_ONLINE_USER':\n            return {\n                ...state,\n                onlineUsers: state.onlineUsers.filter((u)=>u.id !== action.payload)\n            };\n        case 'SET_TYPING_USERS':\n            return {\n                ...state,\n                typingUsers: action.payload\n            };\n        case 'ADD_TYPING_USER':\n            return {\n                ...state,\n                typingUsers: state.typingUsers.includes(action.payload) ? state.typingUsers : [\n                    ...state.typingUsers,\n                    action.payload\n                ]\n            };\n        case 'REMOVE_TYPING_USER':\n            return {\n                ...state,\n                typingUsers: state.typingUsers.filter((id)=>id !== action.payload)\n            };\n        case 'SET_CONNECTION_STATUS':\n            return {\n                ...state,\n                isConnected: action.payload\n            };\n        default:\n            return state;\n    }\n};\nconst initialState = {\n    messages: [],\n    rooms: [],\n    currentRoom: null,\n    onlineUsers: [],\n    typingUsers: [],\n    isConnected: false\n};\nfunction ChatProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(chatReducer, initialState);\n    const { user, isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Initialize WebSocket connection when user is authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (isAuthenticated && user) {\n                const initializeWebSocket = {\n                    \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                        try {\n                            await _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.connect(user.id, user);\n                            // Set up event listeners\n                            const unsubscribeMessage = _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.onMessage({\n                                \"ChatProvider.useEffect.initializeWebSocket.unsubscribeMessage\": (message)=>{\n                                    dispatch({\n                                        type: 'ADD_MESSAGE',\n                                        payload: message\n                                    });\n                                }\n                            }[\"ChatProvider.useEffect.initializeWebSocket.unsubscribeMessage\"]);\n                            const unsubscribeUserJoined = _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.onUserJoined({\n                                \"ChatProvider.useEffect.initializeWebSocket.unsubscribeUserJoined\": (joinedUser)=>{\n                                    dispatch({\n                                        type: 'ADD_ONLINE_USER',\n                                        payload: joinedUser\n                                    });\n                                }\n                            }[\"ChatProvider.useEffect.initializeWebSocket.unsubscribeUserJoined\"]);\n                            const unsubscribeUserLeft = _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.onUserLeft({\n                                \"ChatProvider.useEffect.initializeWebSocket.unsubscribeUserLeft\": (userId)=>{\n                                    dispatch({\n                                        type: 'REMOVE_ONLINE_USER',\n                                        payload: userId\n                                    });\n                                }\n                            }[\"ChatProvider.useEffect.initializeWebSocket.unsubscribeUserLeft\"]);\n                            const unsubscribeTyping = _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.onTyping({\n                                \"ChatProvider.useEffect.initializeWebSocket.unsubscribeTyping\": (userId, isTyping)=>{\n                                    if (isTyping) {\n                                        dispatch({\n                                            type: 'ADD_TYPING_USER',\n                                            payload: userId\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'REMOVE_TYPING_USER',\n                                            payload: userId\n                                        });\n                                    }\n                                }\n                            }[\"ChatProvider.useEffect.initializeWebSocket.unsubscribeTyping\"]);\n                            const unsubscribeConnection = _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.onConnection({\n                                \"ChatProvider.useEffect.initializeWebSocket.unsubscribeConnection\": (isConnected)=>{\n                                    dispatch({\n                                        type: 'SET_CONNECTION_STATUS',\n                                        payload: isConnected\n                                    });\n                                }\n                            }[\"ChatProvider.useEffect.initializeWebSocket.unsubscribeConnection\"]);\n                            // Cleanup function\n                            return ({\n                                \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                    unsubscribeMessage();\n                                    unsubscribeUserJoined();\n                                    unsubscribeUserLeft();\n                                    unsubscribeTyping();\n                                    unsubscribeConnection();\n                                }\n                            })[\"ChatProvider.useEffect.initializeWebSocket\"];\n                        } catch (error) {\n                            console.error('Failed to initialize WebSocket:', error);\n                        }\n                    }\n                }[\"ChatProvider.useEffect.initializeWebSocket\"];\n                initializeWebSocket();\n            }\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    if (_lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.isConnected) {\n                        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.disconnect();\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        }\n    }[\"ChatProvider.useEffect\"], [\n        isAuthenticated,\n        user\n    ]);\n    const sendMessage = (content, chatId, recipientId)=>{\n        if (!_lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.isConnected) {\n            throw new Error('Not connected to chat server');\n        }\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.sendMessage(content, chatId, recipientId);\n    };\n    const loadMessages = async (chatId, recipientId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.getMessages(chatId, recipientId);\n            dispatch({\n                type: 'SET_MESSAGES',\n                payload: response.messages\n            });\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        }\n    };\n    const loadRooms = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.getRooms();\n            dispatch({\n                type: 'SET_ROOMS',\n                payload: response.rooms\n            });\n        } catch (error) {\n            console.error('Failed to load rooms:', error);\n        }\n    };\n    const joinRoom = async (roomId)=>{\n        if (!user) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.joinRoom(roomId, user.id);\n            _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.joinRoom(roomId);\n            // Find and set the current room\n            const room = state.rooms.find((r)=>r.id === roomId);\n            if (room) {\n                dispatch({\n                    type: 'SET_CURRENT_ROOM',\n                    payload: room\n                });\n            }\n        } catch (error) {\n            console.error('Failed to join room:', error);\n            throw error;\n        }\n    };\n    const leaveRoom = async (roomId)=>{\n        if (!user) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.leaveRoom(roomId, user.id);\n            _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.leaveRoom(roomId);\n            if (state.currentRoom?.id === roomId) {\n                dispatch({\n                    type: 'SET_CURRENT_ROOM',\n                    payload: null\n                });\n            }\n        } catch (error) {\n            console.error('Failed to leave room:', error);\n            throw error;\n        }\n    };\n    const setCurrentRoom = (room)=>{\n        dispatch({\n            type: 'SET_CURRENT_ROOM',\n            payload: room\n        });\n    };\n    const startTyping = (chatId, recipientId)=>{\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.sendTyping(true, chatId, recipientId);\n    };\n    const stopTyping = (chatId, recipientId)=>{\n        _lib_websocket__WEBPACK_IMPORTED_MODULE_3__.wsManager.sendTyping(false, chatId, recipientId);\n    };\n    const addReaction = async (messageId, emoji)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.addReaction(messageId, emoji);\n        } catch (error) {\n            console.error('Failed to add reaction:', error);\n            throw error;\n        }\n    };\n    const editMessage = async (messageId, content)=>{\n        try {\n            const updatedMessage = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.updateMessage(messageId, content);\n            dispatch({\n                type: 'UPDATE_MESSAGE',\n                payload: {\n                    id: messageId,\n                    updates: {\n                        content,\n                        is_edited: true\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Failed to edit message:', error);\n            throw error;\n        }\n    };\n    const deleteMessage = async (messageId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.deleteMessage(messageId);\n            dispatch({\n                type: 'DELETE_MESSAGE',\n                payload: messageId\n            });\n        } catch (error) {\n            console.error('Failed to delete message:', error);\n            throw error;\n        }\n    };\n    const value = {\n        ...state,\n        sendMessage,\n        loadMessages,\n        loadRooms,\n        joinRoom,\n        leaveRoom,\n        setCurrentRoom,\n        startTyping,\n        stopTyping,\n        addReaction,\n        editMessage,\n        deleteMessage\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\study\\\\frontend\\\\src\\\\contexts\\\\ChatContext.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, this);\n}\nfunction useChat() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (context === undefined) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ChatContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\nclass ApiClient {\n    constructor(baseURL){\n        this.baseURL = baseURL;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseURL}${endpoint}`;\n        // Get token from localStorage\n        const token =  false ? 0 : null;\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                ...token && {\n                    Authorization: `Bearer ${token}`\n                },\n                ...options.headers\n            },\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Auth endpoints\n    async register(userData) {\n        return this.request('/users/register', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async login(credentials) {\n        return this.request('/users/login', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n    }\n    async getCurrentUser() {\n        return this.request('/users/me');\n    }\n    async updateUser(userId, userData) {\n        return this.request(`/users/${userId}`, {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updatePassword(userId, passwordData) {\n        return this.request(`/users/${userId}/password`, {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    // User endpoints\n    async getUsers(limit = 50) {\n        return this.request(`/users?limit=${limit}`);\n    }\n    async searchUsers(query) {\n        return this.request(`/users/search?q=${encodeURIComponent(query)}`);\n    }\n    async updateUserStatus(status) {\n        return this.request('/users/status', {\n            method: 'PUT',\n            body: JSON.stringify({\n                status\n            })\n        });\n    }\n    // Message endpoints\n    async getMessages(chatId, recipientId, limit = 50) {\n        const params = new URLSearchParams();\n        if (chatId) params.append('chat_id', chatId);\n        if (recipientId) params.append('recipient_id', recipientId);\n        params.append('limit', limit.toString());\n        return this.request(`/messages?${params.toString()}`);\n    }\n    async sendMessage(messageData) {\n        return this.request('/messages', {\n            method: 'POST',\n            body: JSON.stringify(messageData)\n        });\n    }\n    async updateMessage(messageId, content) {\n        return this.request(`/messages/${messageId}`, {\n            method: 'PUT',\n            body: JSON.stringify({\n                content\n            })\n        });\n    }\n    async deleteMessage(messageId) {\n        return this.request(`/messages/${messageId}`, {\n            method: 'DELETE'\n        });\n    }\n    // Room endpoints\n    async getRooms() {\n        return this.request('/rooms');\n    }\n    async getPublicRooms() {\n        return this.request('/rooms/public');\n    }\n    async createRoom(roomData) {\n        return this.request('/rooms', {\n            method: 'POST',\n            body: JSON.stringify(roomData)\n        });\n    }\n    async joinRoom(roomId, userId) {\n        return this.request(`/rooms/${roomId}/join`, {\n            method: 'POST',\n            body: JSON.stringify({\n                user_id: userId\n            })\n        });\n    }\n    async leaveRoom(roomId, userId) {\n        return this.request(`/rooms/${roomId}/leave`, {\n            method: 'POST',\n            body: JSON.stringify({\n                user_id: userId\n            })\n        });\n    }\n    // Reaction endpoints\n    async addReaction(messageId, emoji, emojiName) {\n        return this.request('/reactions', {\n            method: 'POST',\n            body: JSON.stringify({\n                message_id: messageId,\n                emoji,\n                emoji_name: emojiName\n            })\n        });\n    }\n    async removeReaction(reactionId) {\n        return this.request(`/reactions/${reactionId}`, {\n            method: 'DELETE'\n        });\n    }\n    // File upload endpoints\n    async uploadFile(file, onProgress) {\n        const formData = new FormData();\n        formData.append('file', file);\n        return new Promise((resolve, reject)=>{\n            const xhr = new XMLHttpRequest();\n            if (onProgress) {\n                xhr.upload.addEventListener('progress', (e)=>{\n                    if (e.lengthComputable) {\n                        const progress = e.loaded / e.total * 100;\n                        onProgress(progress);\n                    }\n                });\n            }\n            xhr.addEventListener('load', ()=>{\n                if (xhr.status === 200) {\n                    resolve(JSON.parse(xhr.responseText));\n                } else {\n                    reject(new Error(`Upload failed: ${xhr.statusText}`));\n                }\n            });\n            xhr.addEventListener('error', ()=>{\n                reject(new Error('Upload failed'));\n            });\n            const token =  false ? 0 : null;\n            if (token) {\n                xhr.setRequestHeader('Authorization', `Bearer ${token}`);\n            }\n            xhr.open('POST', `${this.baseURL}/files/upload`);\n            xhr.send(formData);\n        });\n    }\n    // Admin endpoints\n    async getAdminStats() {\n        return this.request('/admin/stats');\n    }\n    async getAllUsers() {\n        return this.request('/admin/users');\n    }\n    async deleteUser(userId) {\n        return this.request(`/admin/users/${userId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getAllRooms() {\n        return this.request('/admin/rooms');\n    }\n    async deleteRoom(roomId) {\n        return this.request(`/admin/rooms/${roomId}`, {\n            method: 'DELETE'\n        });\n    }\n}\nconst api = new ApiClient(API_BASE_URL);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/websocket.ts":
/*!******************************!*\
  !*** ./src/lib/websocket.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wsManager: () => (/* binding */ wsManager)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n\nclass WebSocketManager {\n    connect(userId, userInfo) {\n        return new Promise((resolve, reject)=>{\n            if (this.socket?.connected) {\n                resolve();\n                return;\n            }\n            if (this.isConnecting) {\n                return;\n            }\n            this.isConnecting = true;\n            const token = localStorage.getItem('token');\n            if (!token) {\n                this.isConnecting = false;\n                reject(new Error('No authentication token found'));\n                return;\n            }\n            const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(wsUrl, {\n                auth: {\n                    token,\n                    user_id: userId,\n                    user_info: userInfo\n                },\n                transports: [\n                    'websocket'\n                ],\n                upgrade: false\n            });\n            this.socket.on('connect', ()=>{\n                console.log('WebSocket connected');\n                this.isConnecting = false;\n                this.reconnectAttempts = 0;\n                this.notifyConnectionListeners(true);\n                resolve();\n            });\n            this.socket.on('disconnect', (reason)=>{\n                console.log('WebSocket disconnected:', reason);\n                this.notifyConnectionListeners(false);\n                if (reason === 'io server disconnect') {\n                    // Server disconnected, try to reconnect\n                    this.handleReconnect(userId, userInfo);\n                }\n            });\n            this.socket.on('connect_error', (error)=>{\n                console.error('WebSocket connection error:', error);\n                this.isConnecting = false;\n                this.notifyConnectionListeners(false);\n                if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                    this.handleReconnect(userId, userInfo);\n                } else {\n                    reject(error);\n                }\n            });\n            // Message events\n            this.socket.on('message', (data)=>{\n                this.notifyMessageListeners(data);\n            });\n            this.socket.on('user_joined', (data)=>{\n                this.notifyUserJoinedListeners(data);\n            });\n            this.socket.on('user_left', (data)=>{\n                this.notifyUserLeftListeners(data.user_id);\n            });\n            this.socket.on('typing', (data)=>{\n                this.notifyTypingListeners(data.user_id, true);\n            });\n            this.socket.on('stop_typing', (data)=>{\n                this.notifyTypingListeners(data.user_id, false);\n            });\n            this.socket.on('reaction', (data)=>{\n                this.notifyReactionListeners(data);\n            });\n            // Set connection timeout\n            setTimeout(()=>{\n                if (this.isConnecting) {\n                    this.isConnecting = false;\n                    reject(new Error('Connection timeout'));\n                }\n            }, 10000);\n        });\n    }\n    handleReconnect(userId, userInfo) {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.error('Max reconnection attempts reached');\n            return;\n        }\n        this.reconnectAttempts++;\n        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);\n        setTimeout(()=>{\n            this.connect(userId, userInfo).catch((error)=>{\n                console.error('Reconnection failed:', error);\n            });\n        }, delay);\n    }\n    disconnect() {\n        if (this.socket) {\n            this.socket.disconnect();\n            this.socket = null;\n        }\n        this.isConnecting = false;\n        this.reconnectAttempts = 0;\n        this.notifyConnectionListeners(false);\n    }\n    sendMessage(message, chatId, recipientId) {\n        if (!this.socket?.connected) {\n            throw new Error('WebSocket not connected');\n        }\n        this.socket.emit('message', {\n            message,\n            chat_id: chatId,\n            recipient_id: recipientId,\n            timestamp: new Date().toISOString()\n        });\n    }\n    sendTyping(isTyping, chatId, recipientId) {\n        if (!this.socket?.connected) return;\n        this.socket.emit(isTyping ? 'typing' : 'stop_typing', {\n            chat_id: chatId,\n            recipient_id: recipientId\n        });\n    }\n    joinRoom(roomId) {\n        if (!this.socket?.connected) return;\n        this.socket.emit('join_room', {\n            room_id: roomId\n        });\n    }\n    leaveRoom(roomId) {\n        if (!this.socket?.connected) return;\n        this.socket.emit('leave_room', {\n            room_id: roomId\n        });\n    }\n    // Event listener management\n    onMessage(callback) {\n        this.messageListeners.push(callback);\n        return ()=>{\n            this.messageListeners = this.messageListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    onUserJoined(callback) {\n        this.userJoinedListeners.push(callback);\n        return ()=>{\n            this.userJoinedListeners = this.userJoinedListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    onUserLeft(callback) {\n        this.userLeftListeners.push(callback);\n        return ()=>{\n            this.userLeftListeners = this.userLeftListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    onTyping(callback) {\n        this.typingListeners.push(callback);\n        return ()=>{\n            this.typingListeners = this.typingListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    onConnection(callback) {\n        this.connectionListeners.push(callback);\n        return ()=>{\n            this.connectionListeners = this.connectionListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    onReaction(callback) {\n        this.reactionListeners.push(callback);\n        return ()=>{\n            this.reactionListeners = this.reactionListeners.filter((cb)=>cb !== callback);\n        };\n    }\n    // Notification methods\n    notifyMessageListeners(message) {\n        this.messageListeners.forEach((callback)=>callback(message));\n    }\n    notifyUserJoinedListeners(user) {\n        this.userJoinedListeners.forEach((callback)=>callback(user));\n    }\n    notifyUserLeftListeners(userId) {\n        this.userLeftListeners.forEach((callback)=>callback(userId));\n    }\n    notifyTypingListeners(userId, isTyping) {\n        this.typingListeners.forEach((callback)=>callback(userId, isTyping));\n    }\n    notifyConnectionListeners(isConnected) {\n        this.connectionListeners.forEach((callback)=>callback(isConnected));\n    }\n    notifyReactionListeners(data) {\n        this.reactionListeners.forEach((callback)=>callback(data));\n    }\n    get isConnected() {\n        return this.socket?.connected || false;\n    }\n    constructor(){\n        this.socket = null;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectDelay = 1000;\n        this.isConnecting = false;\n        // Event listeners\n        this.messageListeners = [];\n        this.userJoinedListeners = [];\n        this.userLeftListeners = [];\n        this.typingListeners = [];\n        this.connectionListeners = [];\n        this.reactionListeners = [];\n    }\n}\nconst wsManager = new WebSocketManager();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3dlYnNvY2tldC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUc5QyxNQUFNQztJQWVKQyxRQUFRQyxNQUFjLEVBQUVDLFFBQWMsRUFBaUI7UUFDckQsT0FBTyxJQUFJQyxRQUFRLENBQUNDLFNBQVNDO1lBQzNCLElBQUksSUFBSSxDQUFDQyxNQUFNLEVBQUVDLFdBQVc7Z0JBQzFCSDtnQkFDQTtZQUNGO1lBRUEsSUFBSSxJQUFJLENBQUNJLFlBQVksRUFBRTtnQkFDckI7WUFDRjtZQUVBLElBQUksQ0FBQ0EsWUFBWSxHQUFHO1lBQ3BCLE1BQU1DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1YsSUFBSSxDQUFDRCxZQUFZLEdBQUc7Z0JBQ3BCSCxPQUFPLElBQUlPLE1BQU07Z0JBQ2pCO1lBQ0Y7WUFFQSxNQUFNQyxRQUFRQyxRQUFRQyxHQUFHLENBQUNDLGtCQUFrQixJQUFJO1lBRWhELElBQUksQ0FBQ1YsTUFBTSxHQUFHUixvREFBRUEsQ0FBQ2UsT0FBTztnQkFDdEJJLE1BQU07b0JBQ0pSO29CQUNBUyxTQUFTakI7b0JBQ1RrQixXQUFXakI7Z0JBQ2I7Z0JBQ0FrQixZQUFZO29CQUFDO2lCQUFZO2dCQUN6QkMsU0FBUztZQUNYO1lBRUEsSUFBSSxDQUFDZixNQUFNLENBQUNnQixFQUFFLENBQUMsV0FBVztnQkFDeEJDLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixJQUFJLENBQUNoQixZQUFZLEdBQUc7Z0JBQ3BCLElBQUksQ0FBQ2lCLGlCQUFpQixHQUFHO2dCQUN6QixJQUFJLENBQUNDLHlCQUF5QixDQUFDO2dCQUMvQnRCO1lBQ0Y7WUFFQSxJQUFJLENBQUNFLE1BQU0sQ0FBQ2dCLEVBQUUsQ0FBQyxjQUFjLENBQUNLO2dCQUM1QkosUUFBUUMsR0FBRyxDQUFDLDJCQUEyQkc7Z0JBQ3ZDLElBQUksQ0FBQ0QseUJBQXlCLENBQUM7Z0JBRS9CLElBQUlDLFdBQVcsd0JBQXdCO29CQUNyQyx3Q0FBd0M7b0JBQ3hDLElBQUksQ0FBQ0MsZUFBZSxDQUFDM0IsUUFBUUM7Z0JBQy9CO1lBQ0Y7WUFFQSxJQUFJLENBQUNJLE1BQU0sQ0FBQ2dCLEVBQUUsQ0FBQyxpQkFBaUIsQ0FBQ087Z0JBQy9CTixRQUFRTSxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0MsSUFBSSxDQUFDckIsWUFBWSxHQUFHO2dCQUNwQixJQUFJLENBQUNrQix5QkFBeUIsQ0FBQztnQkFFL0IsSUFBSSxJQUFJLENBQUNELGlCQUFpQixHQUFHLElBQUksQ0FBQ0ssb0JBQW9CLEVBQUU7b0JBQ3RELElBQUksQ0FBQ0YsZUFBZSxDQUFDM0IsUUFBUUM7Z0JBQy9CLE9BQU87b0JBQ0xHLE9BQU93QjtnQkFDVDtZQUNGO1lBRUEsaUJBQWlCO1lBQ2pCLElBQUksQ0FBQ3ZCLE1BQU0sQ0FBQ2dCLEVBQUUsQ0FBQyxXQUFXLENBQUNTO2dCQUN6QixJQUFJLENBQUNDLHNCQUFzQixDQUFDRDtZQUM5QjtZQUVBLElBQUksQ0FBQ3pCLE1BQU0sQ0FBQ2dCLEVBQUUsQ0FBQyxlQUFlLENBQUNTO2dCQUM3QixJQUFJLENBQUNFLHlCQUF5QixDQUFDRjtZQUNqQztZQUVBLElBQUksQ0FBQ3pCLE1BQU0sQ0FBQ2dCLEVBQUUsQ0FBQyxhQUFhLENBQUNTO2dCQUMzQixJQUFJLENBQUNHLHVCQUF1QixDQUFDSCxLQUFLYixPQUFPO1lBQzNDO1lBRUEsSUFBSSxDQUFDWixNQUFNLENBQUNnQixFQUFFLENBQUMsVUFBVSxDQUFDUztnQkFDeEIsSUFBSSxDQUFDSSxxQkFBcUIsQ0FBQ0osS0FBS2IsT0FBTyxFQUFFO1lBQzNDO1lBRUEsSUFBSSxDQUFDWixNQUFNLENBQUNnQixFQUFFLENBQUMsZUFBZSxDQUFDUztnQkFDN0IsSUFBSSxDQUFDSSxxQkFBcUIsQ0FBQ0osS0FBS2IsT0FBTyxFQUFFO1lBQzNDO1lBRUEsSUFBSSxDQUFDWixNQUFNLENBQUNnQixFQUFFLENBQUMsWUFBWSxDQUFDUztnQkFDMUIsSUFBSSxDQUFDSyx1QkFBdUIsQ0FBQ0w7WUFDL0I7WUFFQSx5QkFBeUI7WUFDekJNLFdBQVc7Z0JBQ1QsSUFBSSxJQUFJLENBQUM3QixZQUFZLEVBQUU7b0JBQ3JCLElBQUksQ0FBQ0EsWUFBWSxHQUFHO29CQUNwQkgsT0FBTyxJQUFJTyxNQUFNO2dCQUNuQjtZQUNGLEdBQUc7UUFDTDtJQUNGO0lBRVFnQixnQkFBZ0IzQixNQUFjLEVBQUVDLFFBQWMsRUFBRTtRQUN0RCxJQUFJLElBQUksQ0FBQ3VCLGlCQUFpQixJQUFJLElBQUksQ0FBQ0ssb0JBQW9CLEVBQUU7WUFDdkRQLFFBQVFNLEtBQUssQ0FBQztZQUNkO1FBQ0Y7UUFFQSxJQUFJLENBQUNKLGlCQUFpQjtRQUN0QixNQUFNYSxRQUFRLElBQUksQ0FBQ0MsY0FBYyxHQUFHQyxLQUFLQyxHQUFHLENBQUMsR0FBRyxJQUFJLENBQUNoQixpQkFBaUIsR0FBRztRQUV6RUYsUUFBUUMsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUVjLE1BQU0sWUFBWSxFQUFFLElBQUksQ0FBQ2IsaUJBQWlCLENBQUMsQ0FBQyxDQUFDO1FBRXZGWSxXQUFXO1lBQ1QsSUFBSSxDQUFDckMsT0FBTyxDQUFDQyxRQUFRQyxVQUFVd0MsS0FBSyxDQUFDLENBQUNiO2dCQUNwQ04sUUFBUU0sS0FBSyxDQUFDLHdCQUF3QkE7WUFDeEM7UUFDRixHQUFHUztJQUNMO0lBRUFLLGFBQWE7UUFDWCxJQUFJLElBQUksQ0FBQ3JDLE1BQU0sRUFBRTtZQUNmLElBQUksQ0FBQ0EsTUFBTSxDQUFDcUMsVUFBVTtZQUN0QixJQUFJLENBQUNyQyxNQUFNLEdBQUc7UUFDaEI7UUFDQSxJQUFJLENBQUNFLFlBQVksR0FBRztRQUNwQixJQUFJLENBQUNpQixpQkFBaUIsR0FBRztRQUN6QixJQUFJLENBQUNDLHlCQUF5QixDQUFDO0lBQ2pDO0lBRUFrQixZQUFZQyxPQUFlLEVBQUVDLE1BQWUsRUFBRUMsV0FBb0IsRUFBRTtRQUNsRSxJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFQyxXQUFXO1lBQzNCLE1BQU0sSUFBSUssTUFBTTtRQUNsQjtRQUVBLElBQUksQ0FBQ04sTUFBTSxDQUFDMEMsSUFBSSxDQUFDLFdBQVc7WUFDMUJIO1lBQ0FJLFNBQVNIO1lBQ1RJLGNBQWNIO1lBQ2RJLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNuQztJQUNGO0lBRUFDLFdBQVdDLFFBQWlCLEVBQUVULE1BQWUsRUFBRUMsV0FBb0IsRUFBRTtRQUNuRSxJQUFJLENBQUMsSUFBSSxDQUFDekMsTUFBTSxFQUFFQyxXQUFXO1FBRTdCLElBQUksQ0FBQ0QsTUFBTSxDQUFDMEMsSUFBSSxDQUFDTyxXQUFXLFdBQVcsZUFBZTtZQUNwRE4sU0FBU0g7WUFDVEksY0FBY0g7UUFDaEI7SUFDRjtJQUVBUyxTQUFTQyxNQUFjLEVBQUU7UUFDdkIsSUFBSSxDQUFDLElBQUksQ0FBQ25ELE1BQU0sRUFBRUMsV0FBVztRQUM3QixJQUFJLENBQUNELE1BQU0sQ0FBQzBDLElBQUksQ0FBQyxhQUFhO1lBQUVVLFNBQVNEO1FBQU87SUFDbEQ7SUFFQUUsVUFBVUYsTUFBYyxFQUFFO1FBQ3hCLElBQUksQ0FBQyxJQUFJLENBQUNuRCxNQUFNLEVBQUVDLFdBQVc7UUFDN0IsSUFBSSxDQUFDRCxNQUFNLENBQUMwQyxJQUFJLENBQUMsY0FBYztZQUFFVSxTQUFTRDtRQUFPO0lBQ25EO0lBRUEsNEJBQTRCO0lBQzVCRyxVQUFVQyxRQUFvQyxFQUFFO1FBQzlDLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUNDLElBQUksQ0FBQ0Y7UUFDM0IsT0FBTztZQUNMLElBQUksQ0FBQ0MsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDQSxnQkFBZ0IsQ0FBQ0UsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxPQUFPSjtRQUNwRTtJQUNGO0lBRUFLLGFBQWFMLFFBQThCLEVBQUU7UUFDM0MsSUFBSSxDQUFDTSxtQkFBbUIsQ0FBQ0osSUFBSSxDQUFDRjtRQUM5QixPQUFPO1lBQ0wsSUFBSSxDQUFDTSxtQkFBbUIsR0FBRyxJQUFJLENBQUNBLG1CQUFtQixDQUFDSCxNQUFNLENBQUNDLENBQUFBLEtBQU1BLE9BQU9KO1FBQzFFO0lBQ0Y7SUFFQU8sV0FBV1AsUUFBa0MsRUFBRTtRQUM3QyxJQUFJLENBQUNRLGlCQUFpQixDQUFDTixJQUFJLENBQUNGO1FBQzVCLE9BQU87WUFDTCxJQUFJLENBQUNRLGlCQUFpQixHQUFHLElBQUksQ0FBQ0EsaUJBQWlCLENBQUNMLE1BQU0sQ0FBQ0MsQ0FBQUEsS0FBTUEsT0FBT0o7UUFDdEU7SUFDRjtJQUVBUyxTQUFTVCxRQUFxRCxFQUFFO1FBQzlELElBQUksQ0FBQ1UsZUFBZSxDQUFDUixJQUFJLENBQUNGO1FBQzFCLE9BQU87WUFDTCxJQUFJLENBQUNVLGVBQWUsR0FBRyxJQUFJLENBQUNBLGVBQWUsQ0FBQ1AsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxPQUFPSjtRQUNsRTtJQUNGO0lBRUFXLGFBQWFYLFFBQXdDLEVBQUU7UUFDckQsSUFBSSxDQUFDWSxtQkFBbUIsQ0FBQ1YsSUFBSSxDQUFDRjtRQUM5QixPQUFPO1lBQ0wsSUFBSSxDQUFDWSxtQkFBbUIsR0FBRyxJQUFJLENBQUNBLG1CQUFtQixDQUFDVCxNQUFNLENBQUNDLENBQUFBLEtBQU1BLE9BQU9KO1FBQzFFO0lBQ0Y7SUFFQWEsV0FBV2IsUUFBNkIsRUFBRTtRQUN4QyxJQUFJLENBQUNjLGlCQUFpQixDQUFDWixJQUFJLENBQUNGO1FBQzVCLE9BQU87WUFDTCxJQUFJLENBQUNjLGlCQUFpQixHQUFHLElBQUksQ0FBQ0EsaUJBQWlCLENBQUNYLE1BQU0sQ0FBQ0MsQ0FBQUEsS0FBTUEsT0FBT0o7UUFDdEU7SUFDRjtJQUVBLHVCQUF1QjtJQUNmN0IsdUJBQXVCYSxPQUFnQixFQUFFO1FBQy9DLElBQUksQ0FBQ2lCLGdCQUFnQixDQUFDYyxPQUFPLENBQUNmLENBQUFBLFdBQVlBLFNBQVNoQjtJQUNyRDtJQUVRWiwwQkFBMEI0QyxJQUFVLEVBQUU7UUFDNUMsSUFBSSxDQUFDVixtQkFBbUIsQ0FBQ1MsT0FBTyxDQUFDZixDQUFBQSxXQUFZQSxTQUFTZ0I7SUFDeEQ7SUFFUTNDLHdCQUF3QmpDLE1BQWMsRUFBRTtRQUM5QyxJQUFJLENBQUNvRSxpQkFBaUIsQ0FBQ08sT0FBTyxDQUFDZixDQUFBQSxXQUFZQSxTQUFTNUQ7SUFDdEQ7SUFFUWtDLHNCQUFzQmxDLE1BQWMsRUFBRXNELFFBQWlCLEVBQUU7UUFDL0QsSUFBSSxDQUFDZ0IsZUFBZSxDQUFDSyxPQUFPLENBQUNmLENBQUFBLFdBQVlBLFNBQVM1RCxRQUFRc0Q7SUFDNUQ7SUFFUTdCLDBCQUEwQm9ELFdBQW9CLEVBQUU7UUFDdEQsSUFBSSxDQUFDTCxtQkFBbUIsQ0FBQ0csT0FBTyxDQUFDZixDQUFBQSxXQUFZQSxTQUFTaUI7SUFDeEQ7SUFFUTFDLHdCQUF3QkwsSUFBUyxFQUFFO1FBQ3pDLElBQUksQ0FBQzRDLGlCQUFpQixDQUFDQyxPQUFPLENBQUNmLENBQUFBLFdBQVlBLFNBQVM5QjtJQUN0RDtJQUVBLElBQUkrQyxjQUF1QjtRQUN6QixPQUFPLElBQUksQ0FBQ3hFLE1BQU0sRUFBRUMsYUFBYTtJQUNuQzs7YUFqUFFELFNBQXdCO2FBQ3hCbUIsb0JBQW9CO2FBQ3BCSyx1QkFBdUI7YUFDdkJTLGlCQUFpQjthQUNqQi9CLGVBQWU7UUFFdkIsa0JBQWtCO2FBQ1ZzRCxtQkFBbUQsRUFBRTthQUNyREssc0JBQWdELEVBQUU7YUFDbERFLG9CQUFrRCxFQUFFO2FBQ3BERSxrQkFBbUUsRUFBRTthQUNyRUUsc0JBQTBELEVBQUU7YUFDNURFLG9CQUE2QyxFQUFFOztBQXNPekQ7QUFFTyxNQUFNSSxZQUFZLElBQUloRixtQkFBbUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXVwXFxPbmVEcml2ZVxcRGVza3RvcFxcc3R1ZHlcXGZyb250ZW5kXFxzcmNcXGxpYlxcd2Vic29ja2V0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlvLCBTb2NrZXQgfSBmcm9tICdzb2NrZXQuaW8tY2xpZW50JztcbmltcG9ydCB7IE1lc3NhZ2UsIFVzZXIsIFdlYlNvY2tldE1lc3NhZ2UgfSBmcm9tICdAL3R5cGVzJztcblxuY2xhc3MgV2ViU29ja2V0TWFuYWdlciB7XG4gIHByaXZhdGUgc29ja2V0OiBTb2NrZXQgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSByZWNvbm5lY3RBdHRlbXB0cyA9IDA7XG4gIHByaXZhdGUgbWF4UmVjb25uZWN0QXR0ZW1wdHMgPSA1O1xuICBwcml2YXRlIHJlY29ubmVjdERlbGF5ID0gMTAwMDtcbiAgcHJpdmF0ZSBpc0Nvbm5lY3RpbmcgPSBmYWxzZTtcblxuICAvLyBFdmVudCBsaXN0ZW5lcnNcbiAgcHJpdmF0ZSBtZXNzYWdlTGlzdGVuZXJzOiAoKG1lc3NhZ2U6IE1lc3NhZ2UpID0+IHZvaWQpW10gPSBbXTtcbiAgcHJpdmF0ZSB1c2VySm9pbmVkTGlzdGVuZXJzOiAoKHVzZXI6IFVzZXIpID0+IHZvaWQpW10gPSBbXTtcbiAgcHJpdmF0ZSB1c2VyTGVmdExpc3RlbmVyczogKCh1c2VySWQ6IHN0cmluZykgPT4gdm9pZClbXSA9IFtdO1xuICBwcml2YXRlIHR5cGluZ0xpc3RlbmVyczogKCh1c2VySWQ6IHN0cmluZywgaXNUeXBpbmc6IGJvb2xlYW4pID0+IHZvaWQpW10gPSBbXTtcbiAgcHJpdmF0ZSBjb25uZWN0aW9uTGlzdGVuZXJzOiAoKGlzQ29ubmVjdGVkOiBib29sZWFuKSA9PiB2b2lkKVtdID0gW107XG4gIHByaXZhdGUgcmVhY3Rpb25MaXN0ZW5lcnM6ICgoZGF0YTogYW55KSA9PiB2b2lkKVtdID0gW107XG5cbiAgY29ubmVjdCh1c2VySWQ6IHN0cmluZywgdXNlckluZm86IFVzZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgaWYgKHRoaXMuc29ja2V0Py5jb25uZWN0ZWQpIHtcbiAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmICh0aGlzLmlzQ29ubmVjdGluZykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRoaXMuaXNDb25uZWN0aW5nID0gdHJ1ZTtcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBcbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgdGhpcy5pc0Nvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICAgICAgcmVqZWN0KG5ldyBFcnJvcignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnKSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3Qgd3NVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19XU19VUkwgfHwgJ3dzOi8vbG9jYWxob3N0OjgwMDAnO1xuICAgICAgXG4gICAgICB0aGlzLnNvY2tldCA9IGlvKHdzVXJsLCB7XG4gICAgICAgIGF1dGg6IHtcbiAgICAgICAgICB0b2tlbixcbiAgICAgICAgICB1c2VyX2lkOiB1c2VySWQsXG4gICAgICAgICAgdXNlcl9pbmZvOiB1c2VySW5mbyxcbiAgICAgICAgfSxcbiAgICAgICAgdHJhbnNwb3J0czogWyd3ZWJzb2NrZXQnXSxcbiAgICAgICAgdXBncmFkZTogZmFsc2UsXG4gICAgICB9KTtcblxuICAgICAgdGhpcy5zb2NrZXQub24oJ2Nvbm5lY3QnLCAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdXZWJTb2NrZXQgY29ubmVjdGVkJyk7XG4gICAgICAgIHRoaXMuaXNDb25uZWN0aW5nID0gZmFsc2U7XG4gICAgICAgIHRoaXMucmVjb25uZWN0QXR0ZW1wdHMgPSAwO1xuICAgICAgICB0aGlzLm5vdGlmeUNvbm5lY3Rpb25MaXN0ZW5lcnModHJ1ZSk7XG4gICAgICAgIHJlc29sdmUoKTtcbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLnNvY2tldC5vbignZGlzY29ubmVjdCcsIChyZWFzb24pID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBkaXNjb25uZWN0ZWQ6JywgcmVhc29uKTtcbiAgICAgICAgdGhpcy5ub3RpZnlDb25uZWN0aW9uTGlzdGVuZXJzKGZhbHNlKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChyZWFzb24gPT09ICdpbyBzZXJ2ZXIgZGlzY29ubmVjdCcpIHtcbiAgICAgICAgICAvLyBTZXJ2ZXIgZGlzY29ubmVjdGVkLCB0cnkgdG8gcmVjb25uZWN0XG4gICAgICAgICAgdGhpcy5oYW5kbGVSZWNvbm5lY3QodXNlcklkLCB1c2VySW5mbyk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLnNvY2tldC5vbignY29ubmVjdF9lcnJvcicsIChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdXZWJTb2NrZXQgY29ubmVjdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICAgIHRoaXMuaXNDb25uZWN0aW5nID0gZmFsc2U7XG4gICAgICAgIHRoaXMubm90aWZ5Q29ubmVjdGlvbkxpc3RlbmVycyhmYWxzZSk7XG4gICAgICAgIFxuICAgICAgICBpZiAodGhpcy5yZWNvbm5lY3RBdHRlbXB0cyA8IHRoaXMubWF4UmVjb25uZWN0QXR0ZW1wdHMpIHtcbiAgICAgICAgICB0aGlzLmhhbmRsZVJlY29ubmVjdCh1c2VySWQsIHVzZXJJbmZvKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgLy8gTWVzc2FnZSBldmVudHNcbiAgICAgIHRoaXMuc29ja2V0Lm9uKCdtZXNzYWdlJywgKGRhdGE6IE1lc3NhZ2UpID0+IHtcbiAgICAgICAgdGhpcy5ub3RpZnlNZXNzYWdlTGlzdGVuZXJzKGRhdGEpO1xuICAgICAgfSk7XG5cbiAgICAgIHRoaXMuc29ja2V0Lm9uKCd1c2VyX2pvaW5lZCcsIChkYXRhOiBVc2VyKSA9PiB7XG4gICAgICAgIHRoaXMubm90aWZ5VXNlckpvaW5lZExpc3RlbmVycyhkYXRhKTtcbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLnNvY2tldC5vbigndXNlcl9sZWZ0JywgKGRhdGE6IHsgdXNlcl9pZDogc3RyaW5nIH0pID0+IHtcbiAgICAgICAgdGhpcy5ub3RpZnlVc2VyTGVmdExpc3RlbmVycyhkYXRhLnVzZXJfaWQpO1xuICAgICAgfSk7XG5cbiAgICAgIHRoaXMuc29ja2V0Lm9uKCd0eXBpbmcnLCAoZGF0YTogeyB1c2VyX2lkOiBzdHJpbmcgfSkgPT4ge1xuICAgICAgICB0aGlzLm5vdGlmeVR5cGluZ0xpc3RlbmVycyhkYXRhLnVzZXJfaWQsIHRydWUpO1xuICAgICAgfSk7XG5cbiAgICAgIHRoaXMuc29ja2V0Lm9uKCdzdG9wX3R5cGluZycsIChkYXRhOiB7IHVzZXJfaWQ6IHN0cmluZyB9KSA9PiB7XG4gICAgICAgIHRoaXMubm90aWZ5VHlwaW5nTGlzdGVuZXJzKGRhdGEudXNlcl9pZCwgZmFsc2UpO1xuICAgICAgfSk7XG5cbiAgICAgIHRoaXMuc29ja2V0Lm9uKCdyZWFjdGlvbicsIChkYXRhOiBhbnkpID0+IHtcbiAgICAgICAgdGhpcy5ub3RpZnlSZWFjdGlvbkxpc3RlbmVycyhkYXRhKTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTZXQgY29ubmVjdGlvbiB0aW1lb3V0XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKHRoaXMuaXNDb25uZWN0aW5nKSB7XG4gICAgICAgICAgdGhpcy5pc0Nvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICAgICAgICByZWplY3QobmV3IEVycm9yKCdDb25uZWN0aW9uIHRpbWVvdXQnKSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMDAwKTtcbiAgICB9KTtcbiAgfVxuXG4gIHByaXZhdGUgaGFuZGxlUmVjb25uZWN0KHVzZXJJZDogc3RyaW5nLCB1c2VySW5mbzogVXNlcikge1xuICAgIGlmICh0aGlzLnJlY29ubmVjdEF0dGVtcHRzID49IHRoaXMubWF4UmVjb25uZWN0QXR0ZW1wdHMpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ01heCByZWNvbm5lY3Rpb24gYXR0ZW1wdHMgcmVhY2hlZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRoaXMucmVjb25uZWN0QXR0ZW1wdHMrKztcbiAgICBjb25zdCBkZWxheSA9IHRoaXMucmVjb25uZWN0RGVsYXkgKiBNYXRoLnBvdygyLCB0aGlzLnJlY29ubmVjdEF0dGVtcHRzIC0gMSk7XG4gICAgXG4gICAgY29uc29sZS5sb2coYEF0dGVtcHRpbmcgdG8gcmVjb25uZWN0IGluICR7ZGVsYXl9bXMgKGF0dGVtcHQgJHt0aGlzLnJlY29ubmVjdEF0dGVtcHRzfSlgKTtcbiAgICBcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHRoaXMuY29ubmVjdCh1c2VySWQsIHVzZXJJbmZvKS5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgY29uc29sZS5lcnJvcignUmVjb25uZWN0aW9uIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICB9KTtcbiAgICB9LCBkZWxheSk7XG4gIH1cblxuICBkaXNjb25uZWN0KCkge1xuICAgIGlmICh0aGlzLnNvY2tldCkge1xuICAgICAgdGhpcy5zb2NrZXQuZGlzY29ubmVjdCgpO1xuICAgICAgdGhpcy5zb2NrZXQgPSBudWxsO1xuICAgIH1cbiAgICB0aGlzLmlzQ29ubmVjdGluZyA9IGZhbHNlO1xuICAgIHRoaXMucmVjb25uZWN0QXR0ZW1wdHMgPSAwO1xuICAgIHRoaXMubm90aWZ5Q29ubmVjdGlvbkxpc3RlbmVycyhmYWxzZSk7XG4gIH1cblxuICBzZW5kTWVzc2FnZShtZXNzYWdlOiBzdHJpbmcsIGNoYXRJZD86IHN0cmluZywgcmVjaXBpZW50SWQ/OiBzdHJpbmcpIHtcbiAgICBpZiAoIXRoaXMuc29ja2V0Py5jb25uZWN0ZWQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignV2ViU29ja2V0IG5vdCBjb25uZWN0ZWQnKTtcbiAgICB9XG5cbiAgICB0aGlzLnNvY2tldC5lbWl0KCdtZXNzYWdlJywge1xuICAgICAgbWVzc2FnZSxcbiAgICAgIGNoYXRfaWQ6IGNoYXRJZCxcbiAgICAgIHJlY2lwaWVudF9pZDogcmVjaXBpZW50SWQsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9KTtcbiAgfVxuXG4gIHNlbmRUeXBpbmcoaXNUeXBpbmc6IGJvb2xlYW4sIGNoYXRJZD86IHN0cmluZywgcmVjaXBpZW50SWQ/OiBzdHJpbmcpIHtcbiAgICBpZiAoIXRoaXMuc29ja2V0Py5jb25uZWN0ZWQpIHJldHVybjtcblxuICAgIHRoaXMuc29ja2V0LmVtaXQoaXNUeXBpbmcgPyAndHlwaW5nJyA6ICdzdG9wX3R5cGluZycsIHtcbiAgICAgIGNoYXRfaWQ6IGNoYXRJZCxcbiAgICAgIHJlY2lwaWVudF9pZDogcmVjaXBpZW50SWQsXG4gICAgfSk7XG4gIH1cblxuICBqb2luUm9vbShyb29tSWQ6IHN0cmluZykge1xuICAgIGlmICghdGhpcy5zb2NrZXQ/LmNvbm5lY3RlZCkgcmV0dXJuO1xuICAgIHRoaXMuc29ja2V0LmVtaXQoJ2pvaW5fcm9vbScsIHsgcm9vbV9pZDogcm9vbUlkIH0pO1xuICB9XG5cbiAgbGVhdmVSb29tKHJvb21JZDogc3RyaW5nKSB7XG4gICAgaWYgKCF0aGlzLnNvY2tldD8uY29ubmVjdGVkKSByZXR1cm47XG4gICAgdGhpcy5zb2NrZXQuZW1pdCgnbGVhdmVfcm9vbScsIHsgcm9vbV9pZDogcm9vbUlkIH0pO1xuICB9XG5cbiAgLy8gRXZlbnQgbGlzdGVuZXIgbWFuYWdlbWVudFxuICBvbk1lc3NhZ2UoY2FsbGJhY2s6IChtZXNzYWdlOiBNZXNzYWdlKSA9PiB2b2lkKSB7XG4gICAgdGhpcy5tZXNzYWdlTGlzdGVuZXJzLnB1c2goY2FsbGJhY2spO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLm1lc3NhZ2VMaXN0ZW5lcnMgPSB0aGlzLm1lc3NhZ2VMaXN0ZW5lcnMuZmlsdGVyKGNiID0+IGNiICE9PSBjYWxsYmFjayk7XG4gICAgfTtcbiAgfVxuXG4gIG9uVXNlckpvaW5lZChjYWxsYmFjazogKHVzZXI6IFVzZXIpID0+IHZvaWQpIHtcbiAgICB0aGlzLnVzZXJKb2luZWRMaXN0ZW5lcnMucHVzaChjYWxsYmFjayk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMudXNlckpvaW5lZExpc3RlbmVycyA9IHRoaXMudXNlckpvaW5lZExpc3RlbmVycy5maWx0ZXIoY2IgPT4gY2IgIT09IGNhbGxiYWNrKTtcbiAgICB9O1xuICB9XG5cbiAgb25Vc2VyTGVmdChjYWxsYmFjazogKHVzZXJJZDogc3RyaW5nKSA9PiB2b2lkKSB7XG4gICAgdGhpcy51c2VyTGVmdExpc3RlbmVycy5wdXNoKGNhbGxiYWNrKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy51c2VyTGVmdExpc3RlbmVycyA9IHRoaXMudXNlckxlZnRMaXN0ZW5lcnMuZmlsdGVyKGNiID0+IGNiICE9PSBjYWxsYmFjayk7XG4gICAgfTtcbiAgfVxuXG4gIG9uVHlwaW5nKGNhbGxiYWNrOiAodXNlcklkOiBzdHJpbmcsIGlzVHlwaW5nOiBib29sZWFuKSA9PiB2b2lkKSB7XG4gICAgdGhpcy50eXBpbmdMaXN0ZW5lcnMucHVzaChjYWxsYmFjayk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMudHlwaW5nTGlzdGVuZXJzID0gdGhpcy50eXBpbmdMaXN0ZW5lcnMuZmlsdGVyKGNiID0+IGNiICE9PSBjYWxsYmFjayk7XG4gICAgfTtcbiAgfVxuXG4gIG9uQ29ubmVjdGlvbihjYWxsYmFjazogKGlzQ29ubmVjdGVkOiBib29sZWFuKSA9PiB2b2lkKSB7XG4gICAgdGhpcy5jb25uZWN0aW9uTGlzdGVuZXJzLnB1c2goY2FsbGJhY2spO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLmNvbm5lY3Rpb25MaXN0ZW5lcnMgPSB0aGlzLmNvbm5lY3Rpb25MaXN0ZW5lcnMuZmlsdGVyKGNiID0+IGNiICE9PSBjYWxsYmFjayk7XG4gICAgfTtcbiAgfVxuXG4gIG9uUmVhY3Rpb24oY2FsbGJhY2s6IChkYXRhOiBhbnkpID0+IHZvaWQpIHtcbiAgICB0aGlzLnJlYWN0aW9uTGlzdGVuZXJzLnB1c2goY2FsbGJhY2spO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLnJlYWN0aW9uTGlzdGVuZXJzID0gdGhpcy5yZWFjdGlvbkxpc3RlbmVycy5maWx0ZXIoY2IgPT4gY2IgIT09IGNhbGxiYWNrKTtcbiAgICB9O1xuICB9XG5cbiAgLy8gTm90aWZpY2F0aW9uIG1ldGhvZHNcbiAgcHJpdmF0ZSBub3RpZnlNZXNzYWdlTGlzdGVuZXJzKG1lc3NhZ2U6IE1lc3NhZ2UpIHtcbiAgICB0aGlzLm1lc3NhZ2VMaXN0ZW5lcnMuZm9yRWFjaChjYWxsYmFjayA9PiBjYWxsYmFjayhtZXNzYWdlKSk7XG4gIH1cblxuICBwcml2YXRlIG5vdGlmeVVzZXJKb2luZWRMaXN0ZW5lcnModXNlcjogVXNlcikge1xuICAgIHRoaXMudXNlckpvaW5lZExpc3RlbmVycy5mb3JFYWNoKGNhbGxiYWNrID0+IGNhbGxiYWNrKHVzZXIpKTtcbiAgfVxuXG4gIHByaXZhdGUgbm90aWZ5VXNlckxlZnRMaXN0ZW5lcnModXNlcklkOiBzdHJpbmcpIHtcbiAgICB0aGlzLnVzZXJMZWZ0TGlzdGVuZXJzLmZvckVhY2goY2FsbGJhY2sgPT4gY2FsbGJhY2sodXNlcklkKSk7XG4gIH1cblxuICBwcml2YXRlIG5vdGlmeVR5cGluZ0xpc3RlbmVycyh1c2VySWQ6IHN0cmluZywgaXNUeXBpbmc6IGJvb2xlYW4pIHtcbiAgICB0aGlzLnR5cGluZ0xpc3RlbmVycy5mb3JFYWNoKGNhbGxiYWNrID0+IGNhbGxiYWNrKHVzZXJJZCwgaXNUeXBpbmcpKTtcbiAgfVxuXG4gIHByaXZhdGUgbm90aWZ5Q29ubmVjdGlvbkxpc3RlbmVycyhpc0Nvbm5lY3RlZDogYm9vbGVhbikge1xuICAgIHRoaXMuY29ubmVjdGlvbkxpc3RlbmVycy5mb3JFYWNoKGNhbGxiYWNrID0+IGNhbGxiYWNrKGlzQ29ubmVjdGVkKSk7XG4gIH1cblxuICBwcml2YXRlIG5vdGlmeVJlYWN0aW9uTGlzdGVuZXJzKGRhdGE6IGFueSkge1xuICAgIHRoaXMucmVhY3Rpb25MaXN0ZW5lcnMuZm9yRWFjaChjYWxsYmFjayA9PiBjYWxsYmFjayhkYXRhKSk7XG4gIH1cblxuICBnZXQgaXNDb25uZWN0ZWQoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuc29ja2V0Py5jb25uZWN0ZWQgfHwgZmFsc2U7XG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IHdzTWFuYWdlciA9IG5ldyBXZWJTb2NrZXRNYW5hZ2VyKCk7XG4iXSwibmFtZXMiOlsiaW8iLCJXZWJTb2NrZXRNYW5hZ2VyIiwiY29ubmVjdCIsInVzZXJJZCIsInVzZXJJbmZvIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJzb2NrZXQiLCJjb25uZWN0ZWQiLCJpc0Nvbm5lY3RpbmciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJFcnJvciIsIndzVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1dTX1VSTCIsImF1dGgiLCJ1c2VyX2lkIiwidXNlcl9pbmZvIiwidHJhbnNwb3J0cyIsInVwZ3JhZGUiLCJvbiIsImNvbnNvbGUiLCJsb2ciLCJyZWNvbm5lY3RBdHRlbXB0cyIsIm5vdGlmeUNvbm5lY3Rpb25MaXN0ZW5lcnMiLCJyZWFzb24iLCJoYW5kbGVSZWNvbm5lY3QiLCJlcnJvciIsIm1heFJlY29ubmVjdEF0dGVtcHRzIiwiZGF0YSIsIm5vdGlmeU1lc3NhZ2VMaXN0ZW5lcnMiLCJub3RpZnlVc2VySm9pbmVkTGlzdGVuZXJzIiwibm90aWZ5VXNlckxlZnRMaXN0ZW5lcnMiLCJub3RpZnlUeXBpbmdMaXN0ZW5lcnMiLCJub3RpZnlSZWFjdGlvbkxpc3RlbmVycyIsInNldFRpbWVvdXQiLCJkZWxheSIsInJlY29ubmVjdERlbGF5IiwiTWF0aCIsInBvdyIsImNhdGNoIiwiZGlzY29ubmVjdCIsInNlbmRNZXNzYWdlIiwibWVzc2FnZSIsImNoYXRJZCIsInJlY2lwaWVudElkIiwiZW1pdCIsImNoYXRfaWQiLCJyZWNpcGllbnRfaWQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzZW5kVHlwaW5nIiwiaXNUeXBpbmciLCJqb2luUm9vbSIsInJvb21JZCIsInJvb21faWQiLCJsZWF2ZVJvb20iLCJvbk1lc3NhZ2UiLCJjYWxsYmFjayIsIm1lc3NhZ2VMaXN0ZW5lcnMiLCJwdXNoIiwiZmlsdGVyIiwiY2IiLCJvblVzZXJKb2luZWQiLCJ1c2VySm9pbmVkTGlzdGVuZXJzIiwib25Vc2VyTGVmdCIsInVzZXJMZWZ0TGlzdGVuZXJzIiwib25UeXBpbmciLCJ0eXBpbmdMaXN0ZW5lcnMiLCJvbkNvbm5lY3Rpb24iLCJjb25uZWN0aW9uTGlzdGVuZXJzIiwib25SZWFjdGlvbiIsInJlYWN0aW9uTGlzdGVuZXJzIiwiZm9yRWFjaCIsInVzZXIiLCJpc0Nvbm5lY3RlZCIsIndzTWFuYWdlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/websocket.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/lucide-react","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@swc","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Caup%5COneDrive%5CDesktop%5Cstudy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();