{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "e3eLt82/7rwq49pC0K4lq9nHkkQkTaj+dY0/xI+/Dsc="}}}, "functions": {}, "sortedMiddleware": ["/"]}