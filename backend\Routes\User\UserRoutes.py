from fastapi import APIRouter, status, Query, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List
from sqlalchemy.orm import Session
from Controllers.User.UserController import UserController
from Schemas.User.UserSchemas import (
    UserCreate, UserUpdate, UserResponse, UserLogin, UserPublic, LoginResponse
)
from Models.User.UserModel import UserStatus
from database import get_db
from Services.AuthService import AuthService

# Create router
router = APIRouter(prefix="/users", tags=["users"])

# Initialize controller
user_controller = UserController()

# Security scheme
security = HTTPBearer()

# Dependency to get current user from JWT token
async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> str:
    """Get current user ID from JWT token"""
    try:
        token = credentials.credentials
        user_id = AuthService.get_user_id_from_token(token)

        # Verify user exists in database
        user = await user_controller.get_user_by_id(db, user_id)
        return user_id
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

@router.post("/register", response_model=LoginResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """Register a new user"""
    return await user_controller.create_user(db, user_data)

@router.post("/login", response_model=LoginResponse)
async def login_user(login_data: UserLogin, db: Session = Depends(get_db)):
    """Login user"""
    return await user_controller.login_user(db, login_data)

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get current user information"""
    return await user_controller.get_user_by_id(db, current_user_id)

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get current user profile"""
    return await user_controller.get_user_by_id(db, current_user_id)

@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(user_id: str, db: Session = Depends(get_db)):
    """Get user by ID"""
    return await user_controller.get_user_by_id(db, user_id)

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    update_data: UserUpdate,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Update current user profile"""
    return await user_controller.update_user(db, current_user_id, update_data)

@router.get("/", response_model=List[UserPublic])
async def get_all_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get all users (public info only)"""
    return await user_controller.get_all_users(db, skip, limit)

@router.get("/search/", response_model=List[UserPublic])
async def search_users(
    q: str = Query(..., min_length=1),
    limit: int = Query(20, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """Search users by username or display name"""
    return await user_controller.search_users(db, q, limit)

@router.patch("/me/status", response_model=UserResponse)
async def update_user_status(
    status_data: UserStatus,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Update user online status"""
    return await user_controller.update_user_status(db, current_user_id, status_data)

@router.delete("/me")
async def delete_current_user(
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Delete current user account"""
    return await user_controller.delete_user(db, current_user_id)