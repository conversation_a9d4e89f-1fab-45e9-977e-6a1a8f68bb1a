/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-md: 28rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --radius-lg: 0.5rem;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .diff {
    position: relative;
    display: grid;
    width: 100%;
    overflow: hidden;
    webkit-user-select: none;
    user-select: none;
    direction: ltr;
    container-type: inline-size;
    grid-template-columns: auto 1fr;
    &:focus-visible, &:has(.diff-item-1:focus-visible) {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      outline-color: var(--color-base-content);
    }
    &:focus-visible {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      outline-color: var(--color-base-content);
      .diff-resizer {
        min-width: 90cqi;
        max-width: 90cqi;
      }
    }
    &:has(.diff-item-2:focus-visible) {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      .diff-resizer {
        min-width: 10cqi;
        max-width: 10cqi;
      }
    }
    @supports (-webkit-overflow-scrolling: touch) and (overflow: -webkit-paged-x) {
      &:focus {
        .diff-resizer {
          min-width: 10cqi;
          max-width: 10cqi;
        }
      }
      &:has(.diff-item-1:focus) {
        .diff-resizer {
          min-width: 90cqi;
          max-width: 90cqi;
        }
      }
    }
  }
  .menu {
    display: flex;
    width: fit-content;
    flex-direction: column;
    flex-wrap: wrap;
    padding: calc(0.25rem * 2);
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    font-size: 0.875rem;
    :where(li ul) {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      padding-inline-start: calc(0.25rem * 2);
      white-space: nowrap;
      &:before {
        position: absolute;
        inset-inline-start: calc(0.25rem * 0);
        top: calc(0.25rem * 3);
        bottom: calc(0.25rem * 3);
        background-color: var(--color-base-content);
        opacity: 10%;
        width: var(--border);
        content: "";
      }
    }
    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
      display: none;
    }
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      display: grid;
      grid-auto-flow: column;
      align-content: flex-start;
      align-items: center;
      gap: calc(0.25rem * 2);
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      text-align: start;
      transition-property: color, background-color, box-shadow;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      grid-auto-columns: minmax(auto, max-content) auto max-content;
      text-wrap: balance;
      user-select: none;
    }
    :where(li > details > summary) {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      &::-webkit-details-marker {
        display: none;
      }
    }
    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {
      &:after {
        justify-self: flex-end;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        rotate: -135deg;
        translate: 0 -1px;
        transition-property: rotate, translate;
        transition-duration: 0.2s;
        content: "";
        transform-origin: 50% 50%;
        box-shadow: 2px 2px inset;
        pointer-events: none;
      }
    }
    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
      rotate: 45deg;
      translate: 0 1px;
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {
      &.menu-focus, &:focus-visible {
        cursor: pointer;
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        color: var(--color-base-content);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {
      cursor: pointer;
      background-color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;
    }
    :where(li:empty) {
      background-color: var(--color-base-content);
      opacity: 10%;
      margin: 0.5rem 1rem;
      height: 1px;
    }
    :where(li) {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: stretch;
      .badge {
        justify-self: flex-end;
      }
      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        color: var(--menu-active-fg);
        background-color: var(--menu-active-bg);
        background-size: auto, calc(var(--noise) * 100%);
        background-image: none, var(--fx-noise);
        &:not(&:active) {
          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
        }
      }
      &.menu-disabled {
        pointer-events: none;
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    .dropdown:focus-within {
      .menu-dropdown-toggle:after {
        rotate: 45deg;
        translate: 0 1px;
      }
    }
    .dropdown-content {
      margin-top: calc(0.25rem * 2);
      padding: calc(0.25rem * 2);
      &:before {
        display: none;
      }
    }
  }
  .dropdown {
    position: relative;
    display: inline-block;
    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);
    & > *:not(summary):focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .dropdown-content {
      position: absolute;
    }
    &:not(details, .dropdown-open, .dropdown-hover:hover, :focus-within) {
      .dropdown-content {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
    &[popover], .dropdown-content {
      z-index: 999;
      animation: dropdown 0.2s;
      transition-property: opacity, scale, display;
      transition-behavior: allow-discrete;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
    @starting-style {
      &[popover], .dropdown-content {
        scale: 95%;
        opacity: 0;
      }
    }
    &.dropdown-open, &:not(.dropdown-hover):focus, &:focus-within {
      > [tabindex]:first-child {
        pointer-events: none;
      }
      .dropdown-content {
        opacity: 100%;
      }
    }
    &.dropdown-hover:hover {
      .dropdown-content {
        opacity: 100%;
        scale: 100%;
      }
    }
    &:is(details) {
      summary {
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &.dropdown-open, &:focus, &:focus-within {
      .dropdown-content {
        scale: 100%;
      }
    }
    &:where([popover]) {
      background: #0000;
    }
    &[popover] {
      position: fixed;
      color: inherit;
      @supports not (position-area: bottom) {
        margin: auto;
        &.dropdown-open:not(:popover-open) {
          display: none;
          transform-origin: top;
          opacity: 0%;
          scale: 95%;
        }
        &::backdrop {
          background-color: color-mix(in oklab, #000 30%, #0000);
        }
      }
      &:not(.dropdown-open, :popover-open) {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
  }
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
    --btn-shadow: 0 3px 2px -2px var(--btn-bg),
    0 4px 3px -2px var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    }
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    &:focus-visible {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
      --btn-border: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: var(--color-neutral);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          }
          --btn-border: #0000;
          --btn-fg: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
          }
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .loading {
    pointer-events: none;
    display: inline-block;
    aspect-ratio: 1 / 1;
    background-color: currentColor;
    vertical-align: middle;
    width: calc(var(--size-selector, 0.25rem) * 6);
    mask-size: 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
          }
        }
      }
    }
  }
  .toast {
    position: fixed;
    inset-inline-start: auto;
    inset-inline-end: calc(0.25rem * 4);
    top: auto;
    bottom: calc(0.25rem * 4);
    display: flex;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    background-color: transparent;
    translate: var(--toast-x, 0) var(--toast-y, 0);
    width: max-content;
    max-width: calc(100vw - 2rem);
    & > * {
      animation: toast 0.25s ease-out;
    }
    &:where(.toast-start) {
      inset-inline-start: calc(0.25rem * 4);
      inset-inline-end: auto;
      --toast-x: 0;
    }
    &:where(.toast-center) {
      inset-inline-start: calc(1/2 * 100%);
      inset-inline-end: calc(1/2 * 100%);
      --toast-x: -50%;
    }
    &:where(.toast-end) {
      inset-inline-start: auto;
      inset-inline-end: calc(0.25rem * 4);
      --toast-x: 0;
    }
    &:where(.toast-bottom) {
      top: auto;
      bottom: calc(0.25rem * 4);
      --toast-y: 0;
    }
    &:where(.toast-middle) {
      top: calc(1/2 * 100%);
      bottom: auto;
      --toast-y: -50%;
    }
    &:where(.toast-top) {
      top: calc(0.25rem * 4);
      bottom: auto;
      --toast-y: 0;
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="url"]), :where(input[type="email"]) {
      direction: ltr;
    }
    :where(input[type="date"]) {
      display: inline-block;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
  }
  .indicator {
    position: relative;
    display: inline-flex;
    width: max-content;
    :where(.indicator-item) {
      z-index: 1;
      position: absolute;
      white-space: nowrap;
      top: var(--indicator-t, 0);
      bottom: var(--indicator-b, auto);
      left: var(--indicator-s, auto);
      right: var(--indicator-e, 0);
      translate: var(--indicator-x, 50%) var(--indicator-y, -50%);
    }
  }
  .table {
    font-size: 0.875rem;
    position: relative;
    width: 100%;
    border-radius: var(--radius-box);
    text-align: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
    tr.row-hover {
      &, &:nth-child(even) {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-base-200);
          }
        }
      }
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    :where(thead, tfoot) {
      white-space: nowrap;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      }
      font-size: 0.875rem;
      font-weight: 600;
    }
    :where(tfoot) {
      border-top: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
    :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
  }
  .chat-bubble {
    position: relative;
    display: block;
    width: fit-content;
    border-radius: var(--radius-field);
    background-color: var(--color-base-300);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    grid-row-end: 3;
    min-height: 2rem;
    min-width: 2.5rem;
    max-width: 90%;
    &:before {
      position: absolute;
      bottom: calc(0.25rem * 0);
      height: calc(0.25rem * 3);
      width: calc(0.25rem * 3);
      background-color: inherit;
      content: "";
      mask-repeat: no-repeat;
      mask-image: var(--mask-chat);
      mask-position: 0px -1px;
      mask-size: 13px;
    }
  }
  .select {
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    background-color: var(--color-base-100);
    padding-inline-start: calc(0.25rem * 4);
    padding-inline-end: calc(0.25rem * 7);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
    text-overflow: ellipsis;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    border-color: var(--input-color);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    [dir="rtl"] & {
      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);
    }
    select {
      margin-inline-start: calc(0.25rem * -4);
      margin-inline-end: calc(0.25rem * -7);
      width: calc(100% + 2.75rem);
      appearance: none;
      padding-inline-start: calc(0.25rem * 4);
      padding-inline-end: calc(0.25rem * 7);
      height: calc(100% - 2px);
      background: inherit;
      border-radius: inherit;
      border-style: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * -5.5);
        background-image: none;
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> select[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    &:has(> select[disabled]) > select[disabled] {
      cursor: not-allowed;
    }
  }
  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-box);
    outline-width: 2px;
    transition: outline 0.2s ease-in-out;
    outline: 0 solid #0000;
    outline-offset: 2px;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline-color: currentColor;
    }
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: inherit;
    }
    &:where(.card-border) {
      border: var(--border) solid var(--color-base-200);
    }
    &:where(.card-dash) {
      border: var(--border) dashed var(--color-base-200);
    }
    &.image-full {
      display: grid;
      > * {
        grid-column-start: 1;
        grid-row-start: 1;
      }
      > .card-body {
        position: relative;
        color: var(--color-neutral-content);
      }
      :where(figure) {
        overflow: hidden;
        border-radius: inherit;
      }
      > figure img {
        height: 100%;
        object-fit: cover;
        filter: brightness(28%);
      }
    }
    figure {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:has(> input:is(input[type="checkbox"], input[type="radio"])) {
      cursor: pointer;
      user-select: none;
    }
    &:has(> :checked) {
      outline: 2px solid currentColor;
    }
  }
  .menu-horizontal {
    display: inline-flex;
    flex-direction: row;
    & > li:not(.menu-title) > details > ul {
      position: absolute;
      margin-inline-start: calc(0.25rem * 0);
      margin-top: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 2);
      padding-inline-end: calc(0.25rem * 2);
    }
    & > li > details > ul {
      &:before {
        content: none;
      }
    }
    :where(& > li:not(.menu-title) > details > ul) {
      border-radius: var(--radius-box);
      background-color: var(--color-base-100);
      box-shadow: 0 1px 3px 0 oklch(0% 0 0/0.1), 0 1px 2px -1px oklch(0% 0 0/0.1);
    }
  }
  .avatar {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & > div {
      display: block;
      aspect-ratio: 1 / 1;
      overflow: hidden;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    }
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .stats {
    position: relative;
    display: inline-grid;
    grid-auto-flow: column;
    overflow-x: auto;
    border-radius: var(--radius-box);
  }
  .progress {
    position: relative;
    height: calc(0.25rem * 2);
    width: 100%;
    appearance: none;
    overflow: hidden;
    border-radius: var(--radius-box);
    background-color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, currentColor 20%, transparent);
    }
    color: var(--color-base-content);
    &:indeterminate {
      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
      background-size: 200%;
      background-position-x: 15%;
      animation: progress 5s ease-in-out infinite;
      @supports (-moz-appearance: none) {
        &::-moz-progress-bar {
          background-color: transparent;
          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
          background-size: 200%;
          background-position-x: 15%;
          animation: progress 5s ease-in-out infinite;
        }
      }
    }
    @supports (-moz-appearance: none) {
      &::-moz-progress-bar {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
    @supports (-webkit-appearance: none) {
      &::-webkit-progress-bar {
        border-radius: var(--radius-box);
        background-color: transparent;
      }
      &::-webkit-progress-value {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
  }
  .absolute {
    position: absolute;
  }
  .relative {
    position: relative;
  }
  .chat-end {
    place-items: end;
    grid-template-columns: 1fr auto;
    .chat-header {
      grid-column-start: 1;
    }
    .chat-footer {
      grid-column-start: 1;
    }
    .chat-image {
      grid-column-start: 2;
    }
    .chat-bubble {
      grid-column-start: 1;
      border-end-end-radius: 0;
      &:before {
        transform: rotateY(180deg);
        inset-inline-start: 100%;
      }
      [dir="rtl"] &:before {
        transform: rotateY(0deg);
      }
    }
  }
  .chat-start {
    place-items: start;
    grid-template-columns: auto 1fr;
    .chat-header {
      grid-column-start: 2;
    }
    .chat-footer {
      grid-column-start: 2;
    }
    .chat-image {
      grid-column-start: 1;
    }
    .chat-bubble {
      grid-column-start: 2;
      border-end-start-radius: 0;
      &:before {
        transform: rotateY(0deg);
        inset-inline-start: -0.75rem;
      }
      [dir="rtl"] &:before {
        transform: rotateY(180deg);
      }
    }
  }
  .dropdown-end {
    --anchor-h: span-left;
    :where(.dropdown-content) {
      inset-inline-end: calc(0.25rem * 0);
      translate: 0 0;
    }
    &.dropdown-left {
      --anchor-h: left;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
    &.dropdown-right {
      --anchor-h: right;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .hero-content {
    isolation: isolate;
    display: flex;
    max-width: 80rem;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 4);
    padding: calc(0.25rem * 4);
  }
  .btn-active {
    --btn-bg: var(--btn-color, var(--color-base-200));
    @supports (color: color-mix(in lab, red, red)) {
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    }
    --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    isolation: isolate;
  }
  .stack {
    display: inline-grid;
    grid-template-columns: 3px 4px 1fr 4px 3px;
    grid-template-rows: 3px 4px 1fr 4px 3px;
    & > * {
      height: 100%;
      width: 100%;
      &:nth-child(n + 2) {
        width: 100%;
        opacity: 70%;
      }
      &:nth-child(2) {
        z-index: 2;
        opacity: 90%;
      }
      &:nth-child(1) {
        z-index: 3;
        width: 100%;
      }
    }
    &, &.stack-bottom {
      > * {
        grid-column: 3 / 4;
        grid-row: 3 / 6;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 6;
          grid-row: 1 / 4;
        }
      }
    }
    &.stack-top {
      > * {
        grid-column: 3 / 4;
        grid-row: 1 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 6;
          grid-row: 3 / 6;
        }
      }
    }
    &.stack-start {
      > * {
        grid-column: 1 / 4;
        grid-row: 3 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 3 / 6;
          grid-row: 1 / 6;
        }
      }
    }
    &.stack-end {
      > * {
        grid-column: 3 / 6;
        grid-row: 3 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 4;
          grid-row: 1 / 6;
        }
      }
    }
  }
  .z-\[1\] {
    z-index: 1;
  }
  .stat-figure {
    grid-column-start: 2;
    grid-row: span 3 / span 3;
    grid-row-start: 1;
    place-self: center;
    justify-self: flex-end;
  }
  .hero {
    display: grid;
    width: 100%;
    place-items: center;
    background-size: cover;
    background-position: center;
    & > * {
      grid-column-start: 1;
      grid-row-start: 1;
    }
  }
  .stat-value {
    grid-column-start: 1;
    white-space: nowrap;
    font-size: 2rem;
    font-weight: 800;
  }
  .stat-desc {
    grid-column-start: 1;
    white-space: nowrap;
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
    font-size: 0.75rem;
  }
  .stat-title {
    grid-column-start: 1;
    white-space: nowrap;
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
    font-size: 0.75rem;
  }
  .chat-image {
    grid-row: span 2 / span 2;
    align-self: flex-end;
  }
  .chat-footer {
    grid-row-start: 3;
    display: flex;
    gap: calc(0.25rem * 1);
    font-size: 0.6875rem;
  }
  .chat-header {
    grid-row-start: 1;
    display: flex;
    gap: calc(0.25rem * 1);
    font-size: 0.6875rem;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .divider {
    display: flex;
    height: calc(0.25rem * 4);
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    white-space: nowrap;
    margin: var(--divider-m, 1rem 0);
    --divider-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
    &:before, &:after {
      content: "";
      height: calc(0.25rem * 0.5);
      width: 100%;
      flex-grow: 1;
      background-color: var(--divider-color);
    }
    @media print {
      &:before, &:after {
        border: 0.5px solid;
      }
    }
    &:not(:empty) {
      gap: calc(0.25rem * 4);
    }
  }
  .filter {
    display: flex;
    flex-wrap: wrap;
    input[type="radio"] {
      width: auto;
    }
    input {
      overflow: hidden;
      opacity: 100%;
      scale: 1;
      transition: margin 0.1s, opacity 0.3s, padding 0.3s, border-width 0.1s;
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * 1);
      }
      &.filter-reset {
        aspect-ratio: 1 / 1;
        &::after {
          content: "×";
        }
      }
    }
    &:not(:has(input:checked:not(.filter-reset))) {
      .filter-reset, input[type="reset"] {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
    &:has(input:checked:not(.filter-reset)) {
      input:not(:checked, .filter-reset, input[type="reset"]) {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .label {
    display: inline-flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    white-space: nowrap;
    color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, currentColor 60%, transparent);
    }
    &:has(input) {
      cursor: pointer;
    }
    &:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * 3);
        border-inline-end: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3);
        margin-inline-end: calc(0.25rem * -3);
        border-inline-start: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
    }
  }
  .join-item {
    &:where(*:not(:first-child, :disabled, [disabled], .btn-disabled)) {
      margin-inline-start: calc(var(--border, 1px) * -1);
      margin-block-start: 0;
    }
    &:where(*:is(:disabled, [disabled], .btn-disabled)) {
      border-width: var(--border, 1px) 0 var(--border, 1px) var(--border, 1px);
    }
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .status {
    display: inline-block;
    aspect-ratio: 1 / 1;
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    background-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, #000 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
    box-shadow: 0 2px 3px -1px currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
    }
  }
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    font-size: 0.875rem;
    width: fit-content;
    padding-inline: calc(0.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    height: var(--size);
  }
  .navbar {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 0.5rem;
    min-height: 4rem;
  }
  .footer {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    place-items: start;
    column-gap: calc(0.25rem * 4);
    row-gap: calc(0.25rem * 10);
    font-size: 0.875rem;
    line-height: 1.25rem;
    & > * {
      display: grid;
      place-items: start;
      gap: calc(0.25rem * 2);
    }
    &.footer-center {
      grid-auto-flow: column dense;
      place-items: center;
      text-align: center;
      & > * {
        place-items: center;
      }
    }
  }
  .stat {
    display: inline-grid;
    width: 100%;
    column-gap: calc(0.25rem * 4);
    padding-inline: calc(0.25rem * 6);
    padding-block: calc(0.25rem * 4);
    grid-template-columns: repeat(1, 1fr);
    &:not(:last-child) {
      border-inline-end: var(--border) dashed currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
      }
      border-block-end: none;
    }
  }
  .navbar-end {
    display: inline-flex;
    align-items: center;
    width: 50%;
    justify-content: flex-end;
  }
  .navbar-start {
    display: inline-flex;
    align-items: center;
    width: 50%;
    justify-content: flex-start;
  }
  .card-body {
    display: flex;
    flex: auto;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    padding: var(--card-p, 1.5rem);
    font-size: var(--card-fs, 0.875rem);
    :where(p) {
      flex-grow: 1;
    }
  }
  .navbar-center {
    display: inline-flex;
    align-items: center;
    flex-shrink: 0;
  }
  .alert {
    display: grid;
    align-items: center;
    gap: calc(0.25rem * 4);
    border-radius: var(--radius-box);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 3);
    color: var(--color-base-content);
    background-color: var(--alert-color, var(--color-base-200));
    justify-content: start;
    justify-items: start;
    grid-auto-flow: column;
    grid-template-columns: auto;
    text-align: start;
    border: var(--border) solid var(--color-base-200);
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px #000, 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px color-mix( in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), #0000 ), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    }
    &:has(:nth-child(2)) {
      grid-template-columns: auto minmax(auto, 1fr);
    }
    &.alert-outline {
      background-color: transparent;
      color: var(--alert-color);
      box-shadow: none;
      background-image: none;
    }
    &.alert-dash {
      background-color: transparent;
      color: var(--alert-color);
      border-style: dashed;
      box-shadow: none;
      background-image: none;
    }
    &.alert-soft {
      color: var(--alert-color, var(--color-base-content));
      background: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        background: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100) );
      }
      border-color: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100) );
      }
      box-shadow: none;
      background-image: none;
    }
  }
  .card-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: calc(0.25rem * 2);
  }
  .card-title {
    display: flex;
    align-items: center;
    gap: calc(0.25rem * 2);
    font-size: var(--cardtitle-fs, 1.125rem);
    font-weight: 600;
  }
  .join {
    display: inline-flex;
    align-items: stretch;
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
    :where(.join-item) {
      border-start-start-radius: var(--join-ss, 0);
      border-start-end-radius: var(--join-se, 0);
      border-end-start-radius: var(--join-es, 0);
      border-end-end-radius: var(--join-ee, 0);
      * {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:first-child) {
      --join-ss: var(--radius-field);
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: 0;
      }
    }
    > .join-item:where(:last-child) {
      --join-ss: 0;
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      :where(.join-item) {
        --join-ss: 0;
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:only-child) {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
  }
  .chat {
    display: grid;
    column-gap: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 1);
    --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .table {
    display: table;
  }
  .card-side {
    align-items: stretch;
    flex-direction: row;
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: inherit;
    }
    figure > * {
      max-width: unset;
    }
    :where(figure > *) {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .btn-circle {
    border-radius: calc(infinity * 1px);
    padding-inline: calc(0.25rem * 0);
    width: var(--size);
    height: var(--size);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }
  .min-h-\[80vh\] {
    min-height: 80vh;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .loading-lg {
    width: calc(var(--size-selector, 0.25rem) * 7);
  }
  .loading-sm {
    width: calc(var(--size-selector, 0.25rem) * 5);
  }
  .loading-xs {
    width: calc(var(--size-selector, 0.25rem) * 4);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-full {
    width: 100%;
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .flex-1 {
    flex: 1;
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .link {
    cursor: pointer;
    text-decoration-line: underline;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .stats-horizontal {
    grid-auto-flow: column;
    overflow-x: auto;
    .stat:not(:last-child) {
      border-inline-end: var(--border) dashed currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
      }
      border-block-end: none;
    }
  }
  .grid-flow-col {
    grid-auto-flow: column;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .menu-sm {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2.5);
      padding-block: calc(0.25rem * 1);
      font-size: 0.75rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 2);
    }
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .badge-ghost {
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
    background-image: none;
  }
  .alert-error {
    border-color: var(--color-error);
    color: var(--color-error-content);
    --alert-color: var(--color-error);
  }
  .border-base-100 {
    border-color: var(--color-base-100);
  }
  .border-base-300 {
    border-color: var(--color-base-300);
  }
  .chat-bubble-accent {
    background-color: var(--color-accent);
    color: var(--color-accent-content);
  }
  .chat-bubble-primary {
    background-color: var(--color-primary);
    color: var(--color-primary-content);
  }
  .table-zebra {
    tbody {
      tr {
        &:where(:nth-child(even)) {
          background-color: var(--color-base-200);
          :where(.table-pin-cols tr th) {
            background-color: var(--color-base-200);
          }
        }
        &.row-hover {
          &, &:where(:nth-child(even)) {
            &:hover {
              @media (hover: hover) {
                background-color: var(--color-base-300);
              }
            }
          }
        }
      }
    }
  }
  .bg-base-100 {
    background-color: var(--color-base-100);
  }
  .bg-base-200 {
    background-color: var(--color-base-200);
  }
  .bg-base-300 {
    background-color: var(--color-base-300);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-error\/10 {
    background-color: var(--color-error);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-error) 10%, transparent);
    }
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-indigo-500 {
    background-color: var(--color-indigo-500);
  }
  .bg-neutral {
    background-color: var(--color-neutral);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-success {
    background-color: var(--color-success);
  }
  .bg-success\/10 {
    background-color: var(--color-success);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-success) 10%, transparent);
    }
  }
  .bg-teal-500 {
    background-color: var(--color-teal-500);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-info {
    --tw-gradient-to: var(--color-info);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .loading-spinner {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .checkbox-sm {
    padding: 0.1875rem;
    --size: calc(var(--size-selector, 0.25rem) * 5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-10 {
    padding: calc(var(--spacing) * 10);
  }
  .menu-title {
    padding-inline: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
    font-size: 0.875rem;
    font-weight: 600;
  }
  .badge-sm {
    --size: calc(var(--size-selector, 0.25rem) * 5);
    font-size: 0.75rem;
    padding-inline: calc(0.25rem * 2.5 - var(--border));
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .link-neutral {
    color: var(--color-neutral);
    @media (hover: hover) {
      &:hover {
        color: var(--color-neutral);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-neutral) 80%, #000);
        }
      }
    }
  }
  .link-primary {
    color: var(--color-primary);
    @media (hover: hover) {
      &:hover {
        color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-primary) 80%, #000);
        }
      }
    }
  }
  .text-accent {
    color: var(--color-accent);
  }
  .text-base-content {
    color: var(--color-base-content);
  }
  .text-base-content\/30 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 30%, transparent);
    }
  }
  .text-base-content\/50 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }
  .text-base-content\/70 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 70%, transparent);
    }
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-error {
    color: var(--color-error);
  }
  .text-info {
    color: var(--color-info);
  }
  .text-neutral-content {
    color: var(--color-neutral-content);
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-primary-content {
    color: var(--color-primary-content);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-secondary {
    color: var(--color-secondary);
  }
  .text-success {
    color: var(--color-success);
  }
  .text-warning {
    color: var(--color-warning);
  }
  .text-white {
    color: var(--color-white);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .link-hover {
    text-decoration-line: none;
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .btn-ghost {
    &:not(.btn-active, :hover, :active:focus, :focus-visible) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-border: #0000;
      --btn-noise: none;
      &:not(:disabled, [disabled], .btn-disabled) {
        outline-color: currentColor;
        --btn-fg: currentColor;
      }
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: currentColor;
      }
    }
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .btn-outline {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .btn-lg {
    --fontsize: 1.125rem;
    --btn-p: 1.25rem;
    --size: calc(var(--size-field, 0.25rem) * 12);
  }
  .btn-sm {
    --fontsize: 0.75rem;
    --btn-p: 0.75rem;
    --size: calc(var(--size-field, 0.25rem) * 8);
  }
  .badge-accent {
    --badge-color: var(--color-accent);
    --badge-fg: var(--color-accent-content);
  }
  .badge-error {
    --badge-color: var(--color-error);
    --badge-fg: var(--color-error-content);
  }
  .badge-info {
    --badge-color: var(--color-info);
    --badge-fg: var(--color-info-content);
  }
  .badge-neutral {
    --badge-color: var(--color-neutral);
    --badge-fg: var(--color-neutral-content);
  }
  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }
  .badge-secondary {
    --badge-color: var(--color-secondary);
    --badge-fg: var(--color-secondary-content);
  }
  .badge-success {
    --badge-color: var(--color-success);
    --badge-fg: var(--color-success-content);
  }
  .badge-warning {
    --badge-color: var(--color-warning);
    --badge-fg: var(--color-warning-content);
  }
  .btn-accent {
    --btn-color: var(--color-accent);
    --btn-fg: var(--color-accent-content);
  }
  .btn-info {
    --btn-color: var(--color-info);
    --btn-fg: var(--color-info-content);
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
  .input-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .hover\:text-base-content {
    &:hover {
      @media (hover: hover) {
        color: var(--color-base-content);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:col-span-1 {
    @media (width >= 64rem) {
      grid-column: span 1 / span 1;
    }
  }
  .lg\:col-span-2 {
    @media (width >= 64rem) {
      grid-column: span 2 / span 2;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .xl\:grid-cols-4 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}
.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}
.typing-dots {
  display: inline-block;
}
.typing-dots span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #94a3b8;
  margin: 0 1px;
  animation: typing 1.4s infinite ease-in-out;
}
.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}
@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
    @supports (color: color-mix(in lab, red, red)) {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
    @supports (color: color-mix(in lab, red, red)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
    }
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
    }
  }
}

